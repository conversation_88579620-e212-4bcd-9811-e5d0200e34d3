{include file="$template/header.tpl"}

<div class="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="max-w-md mx-auto">
        <!-- Login Card -->
        <div class="panel">
            <div class="panel-heading text-center">
                <h1 class="text-2xl font-bold text-white mb-2">
                    <i data-lucide="log-in" class="w-6 h-6 mr-2 inline text-blue-400"></i>
                    Login to Your Account
                </h1>
                <p class="text-gray-400">Access your hosting dashboard</p>
            </div>

            <div class="panel-body">
                {if $incorrect}
                    <div class="alert alert-danger mb-6">
                        <i data-lucide="alert-circle" class="w-4 h-4 mr-2 inline"></i>
                        {$LANG.loginincorrect}
                    </div>
                {/if}

                {if $verificationId}
                    <div class="alert alert-info mb-6">
                        <i data-lucide="info" class="w-4 h-4 mr-2 inline"></i>
                        {$LANG.verificationKeyExpired}
                    </div>
                {/if}

                {if $ssoredirect}
                    <div class="alert alert-info mb-6">
                        <i data-lucide="info" class="w-4 h-4 mr-2 inline"></i>
                        {$LANG.ssoredirect}
                    </div>
                {/if}

                <form method="post" action="{$systemurl}dologin.php" class="login-form">
                    <div class="form-group">
                        <label for="inputEmail" class="form-label">
                            <i data-lucide="mail" class="w-4 h-4 mr-2 inline"></i>
                            {$LANG.clientareaemail}
                        </label>
                        <input type="email"
                               name="username"
                               id="inputEmail"
                               class="form-control"
                               placeholder="{$LANG.enteremail}"
                               autofocus
                               required>
                    </div>

                    <div class="form-group">
                        <label for="inputPassword" class="form-label">
                            <i data-lucide="lock" class="w-4 h-4 mr-2 inline"></i>
                            {$LANG.clientareapassword}
                        </label>
                        <input type="password"
                               name="password"
                               id="inputPassword"
                               class="form-control"
                               placeholder="{$LANG.clientareapassword}"
                               required>
                    </div>

                    {if $captcha}
                        <div class="form-group">
                            <label class="form-label">
                                <i data-lucide="shield" class="w-4 h-4 mr-2 inline"></i>
                                {$LANG.captchaverify}
                            </label>
                            <div class="captcha-container">
                                {$captcha}
                            </div>
                        </div>
                    {/if}

                    <div class="flex items-center justify-between mb-6">
                        <label class="flex items-center">
                            <input type="checkbox" name="rememberme" class="mr-2">
                            <span class="text-sm text-gray-300">{$LANG.loginrememberme}</span>
                        </label>
                        <a href="pwreset.php" class="text-sm text-blue-400 hover:text-blue-300 transition-colors duration-300">
                            {$LANG.forgotpw}
                        </a>
                    </div>

                    <button type="submit" class="btn btn-primary w-full mb-4">
                        <i data-lucide="log-in" class="w-4 h-4 mr-2"></i>
                        {$LANG.loginbutton}
                    </button>
                </form>

                <!-- Social Login Options -->
                {if $providerDisplayNames}
                    <div class="text-center mb-4">
                        <div class="relative">
                            <div class="absolute inset-0 flex items-center">
                                <div class="w-full border-t border-slate-700"></div>
                            </div>
                            <div class="relative flex justify-center text-sm">
                                <span class="px-2 bg-slate-900 text-gray-400">Or continue with</span>
                            </div>
                        </div>
                    </div>

                    <div class="space-y-2">
                        {foreach $providerDisplayNames as $provider => $displayName}
                            <a href="{$WEB_ROOT}/oauth/login.php?provider={$provider}"
                               class="btn btn-secondary w-full">
                                <i data-lucide="external-link" class="w-4 h-4 mr-2"></i>
                                {$displayName}
                            </a>
                        {/foreach}
                    </div>
                {/if}
            </div>

        </div>

        <!-- Additional Info -->
        <div class="mt-8 text-center">
            <div class="bg-gradient-to-r from-blue-500/10 to-purple-500/10 border border-blue-500/20 rounded-xl p-6">
                <h3 class="text-lg font-semibold text-white mb-2">
                    <i data-lucide="shield-check" class="w-5 h-5 mr-2 inline text-green-400"></i>
                    Secure Login
                </h3>
                <p class="text-gray-300 text-sm">
                    Your connection is secured with 256-bit SSL encryption and protected by advanced security measures.
                </p>
            </div>
        </div>
    </div>
</div>
            </div>
        </div>
    </main>

{include file="$template/footer.tpl"}
