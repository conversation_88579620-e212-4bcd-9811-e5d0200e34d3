{include file="$template/header.tpl"}

<!-- Compact Login Section -->
<section class="py-12 px-4 sm:px-6 lg:px-8">
    <div class="w-full max-w-md mx-auto">
        <!-- Compact <PERSON> -->
        <div class="flex items-center justify-center gap-3 mb-6">
            <div class="flex items-center px-3 py-1.5 bg-gradient-to-r from-blue-500/20 to-purple-500/20 border border-blue-500/30 rounded-full text-xs font-bold text-white shadow-lg">
                <i data-lucide="shield-check" class="w-3 h-3 mr-1.5"></i>
                Secure Login
            </div>
            <div class="flex items-center px-3 py-1.5 bg-gradient-to-r from-emerald-500/20 to-teal-500/20 border border-emerald-500/30 rounded-full text-xs font-bold text-white shadow-lg">
                <i data-lucide="zap" class="w-3 h-3 mr-1.5"></i>
                Instant Access
            </div>
        </div>

        <!-- Compact Main Heading -->
        <div class="text-center mb-6">
            <h1 class="text-3xl md:text-4xl font-black leading-tight mb-3">
                <span class="bg-gradient-to-r from-blue-400 via-purple-500 via-pink-500 to-orange-500 bg-clip-text text-transparent">
                    Welcome Back
                </span>
            </h1>
            <p class="text-base text-gray-300">Access your hosting dashboard</p>
        </div>

        <!-- Compact Login Card -->
        <div class="bg-slate-900/70 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-6 shadow-2xl">
                <!-- Compact Login Form Header -->


                <!-- Alert Messages -->
                {if $incorrect}
                    <div class="bg-gradient-to-r from-red-500/10 to-pink-500/10 border border-red-500/30 rounded-xl p-4 mb-6">
                        <div class="flex items-center">
                            <i data-lucide="alert-circle" class="w-5 h-5 mr-3 text-red-400"></i>
                            <span class="text-red-300 font-medium">{$LANG.loginincorrect}</span>
                        </div>
                    </div>
                {/if}

                {if $verificationId}
                    <div class="bg-gradient-to-r from-blue-500/10 to-cyan-500/10 border border-blue-500/30 rounded-xl p-4 mb-6">
                        <div class="flex items-center">
                            <i data-lucide="info" class="w-5 h-5 mr-3 text-blue-400"></i>
                            <span class="text-blue-300 font-medium">{$LANG.verificationKeyExpired}</span>
                        </div>
                    </div>
                {/if}

                {if $ssoredirect}
                    <div class="bg-gradient-to-r from-blue-500/10 to-cyan-500/10 border border-blue-500/30 rounded-xl p-4 mb-6">
                        <div class="flex items-center">
                            <i data-lucide="info" class="w-5 h-5 mr-3 text-blue-400"></i>
                            <span class="text-blue-300 font-medium">{$LANG.ssoredirect}</span>
                        </div>
                    </div>
                {/if}

                <form method="post" action="{routePath('login-validate')}" class="space-y-4 login-form" role="form">
                    <div class="space-y-1">
                        <label for="inputEmail" class="block text-sm font-medium text-gray-300">
                            <i data-lucide="mail" class="w-4 h-4 mr-2 inline text-blue-400"></i>
                            {$LANG.clientareaemail}
                        </label>
                        <input type="email"
                               name="username"
                               id="inputEmail"
                               class="w-full px-3 py-2.5 bg-slate-800/50 border border-slate-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 transition-all duration-300"
                               placeholder="{$LANG.enteremail}"
                               autofocus
                               required>
                    </div>

                    <div class="space-y-1">
                        <label for="inputPassword" class="block text-sm font-medium text-gray-300">
                            <i data-lucide="lock" class="w-4 h-4 mr-2 inline text-purple-400"></i>
                            {$LANG.clientareapassword}
                        </label>
                        <input type="password"
                               name="password"
                               id="inputPassword"
                               class="w-full px-3 py-2.5 bg-slate-800/50 border border-slate-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:border-purple-500/50 transition-all duration-300"
                               placeholder="{$LANG.clientareapassword}"
                               required>
                    </div>

                    {if $captcha->isEnabled()}
                        <div class="form-group text-center margin-bottom">
                            <label class="form-label">
                                <i data-lucide="shield" class="w-4 h-4 mr-2 inline text-emerald-400"></i>
                                {$LANG.captchaverify}
                            </label>
                            {include file="$template/includes/captcha.tpl"}
                        </div>
                    {/if}

                    <div class="flex items-center justify-between">
                        <label class="flex items-center cursor-pointer">
                            <input type="checkbox" name="rememberme" class="w-4 h-4 text-blue-500 bg-slate-800 border-slate-600 rounded focus:ring-blue-500/50 focus:ring-2">
                            <span class="ml-2 text-sm text-gray-300 hover:text-white transition-colors">{$LANG.loginrememberme}</span>
                        </label>
                        <a href="pwreset.php" class="text-sm text-blue-400 hover:text-blue-300 transition-colors duration-300 font-medium">
                            {$LANG.forgotpw}
                        </a>
                    </div>

                    <button type="submit" id="login" class="w-full bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 text-white px-6 py-3 rounded-lg font-bold shadow-2xl hover:shadow-blue-500/25 transition-all duration-300 transform hover:scale-[1.02] focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:ring-offset-2 focus:ring-offset-slate-900{$captcha->getButtonClass($captchaForm)}">
                        <i data-lucide="log-in" class="w-4 h-4 mr-2 inline"></i>
                        {$LANG.loginbutton}
                    </button>
                </form>

                <!-- Social Login Options -->
                {if $providerDisplayNames}
                    <div class="text-center my-4">
                        <div class="relative">
                            <div class="absolute inset-0 flex items-center">
                                <div class="w-full border-t border-slate-600/50"></div>
                            </div>
                            <div class="relative flex justify-center text-sm">
                                <span class="px-3 bg-slate-900/70 text-gray-400 font-medium">Or continue with</span>
                            </div>
                        </div>
                    </div>

                    <div class="space-y-2">
                        {foreach $providerDisplayNames as $provider => $displayName}
                            <a href="{$WEB_ROOT}/oauth/login.php?provider={$provider}"
                               class="w-full bg-slate-800/50 border border-slate-600/50 text-white px-4 py-2.5 rounded-lg font-medium hover:bg-slate-700/50 hover:border-slate-500/50 transition-all duration-300 flex items-center justify-center">
                                <i data-lucide="external-link" class="w-4 h-4 mr-2"></i>
                                {$displayName}
                            </a>
                        {/foreach}
                    </div>
                {/if}
            </div>
        </div>

        <!-- Compact Footer -->
        <div class="mt-4 text-center">
            <p class="text-gray-400 text-sm mb-2">
                Don't have an account?
                <a href="register.php" class="text-blue-400 hover:text-blue-300 font-medium transition-colors">
                    Create one now
                </a>
            </p>
            <div class="flex items-center justify-center space-x-4 text-xs text-gray-500">
                <a href="contact.php" class="hover:text-gray-300 transition-colors">Support</a>
                <span>•</span>
                <a href="privacy.php" class="hover:text-gray-300 transition-colors">Privacy</a>
                <span>•</span>
                <a href="terms.php" class="hover:text-gray-300 transition-colors">Terms</a>
            </div>
        </div>
    </div>
</section>

{include file="$template/footer.tpl"}
