{include file="$template/header.tpl"}

<!-- Hero-style Login Section -->

    <!-- Hero gradient background -->
    <div class="absolute inset-0 hero-gradient"></div>
    <div class="absolute inset-0 bg-gradient-to-br from-slate-950 via-slate-900 to-slate-950"></div>

    <div class="relative z-10 w-full max-w-md mx-auto">
        <!-- Hero <PERSON>ges -->
        <div class="flex flex-wrap items-center justify-center gap-3 mb-8">
            <div class="flex items-center px-4 py-2 bg-gradient-to-r from-blue-500/20 to-purple-500/20 border border-blue-500/30 rounded-full text-xs font-bold text-white shadow-lg">
                <i data-lucide="shield-check" class="w-3 h-3 mr-2"></i>
                Secure Login
            </div>
            <div class="flex items-center px-4 py-2 bg-gradient-to-r from-emerald-500/20 to-teal-500/20 border border-emerald-500/30 rounded-full text-xs font-bold text-white shadow-lg">
                <i data-lucide="zap" class="w-3 h-3 mr-2"></i>
                Instant Access
            </div>
        </div>

        <!-- Main Heading -->
        <div class="text-center mb-8">
            <h1 class="text-4xl md:text-5xl font-black leading-tight mb-4">
                <span class="bg-gradient-to-r from-blue-400 via-purple-500 via-pink-500 to-orange-500 bg-clip-text text-transparent">
                    Welcome Back
                </span>
            </h1>
            <p class="text-lg text-gray-300 mb-2">Access your hosting dashboard</p>
            <p class="text-sm text-gray-400">Manage your enterprise infrastructure</p>
        </div>

        <!-- Login Card -->
        <div class="relative">
            <div class="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-purple-500/5 rounded-3xl"></div>
            <div class="relative bg-slate-900/70 backdrop-blur-sm border border-slate-700/50 rounded-3xl p-8 shadow-2xl">
                <!-- Login Form Header -->
                <div class="text-center mb-6">
                    <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
                        <i data-lucide="log-in" class="w-8 h-8 text-white"></i>
                    </div>
                    <h2 class="text-xl font-bold text-white">Sign In</h2>
                </div>

                <!-- Alert Messages -->
                {if $incorrect}
                    <div class="bg-gradient-to-r from-red-500/10 to-pink-500/10 border border-red-500/30 rounded-xl p-4 mb-6">
                        <div class="flex items-center">
                            <i data-lucide="alert-circle" class="w-5 h-5 mr-3 text-red-400"></i>
                            <span class="text-red-300 font-medium">{$LANG.loginincorrect}</span>
                        </div>
                    </div>
                {/if}

                {if $verificationId}
                    <div class="bg-gradient-to-r from-blue-500/10 to-cyan-500/10 border border-blue-500/30 rounded-xl p-4 mb-6">
                        <div class="flex items-center">
                            <i data-lucide="info" class="w-5 h-5 mr-3 text-blue-400"></i>
                            <span class="text-blue-300 font-medium">{$LANG.verificationKeyExpired}</span>
                        </div>
                    </div>
                {/if}

                {if $ssoredirect}
                    <div class="bg-gradient-to-r from-blue-500/10 to-cyan-500/10 border border-blue-500/30 rounded-xl p-4 mb-6">
                        <div class="flex items-center">
                            <i data-lucide="info" class="w-5 h-5 mr-3 text-blue-400"></i>
                            <span class="text-blue-300 font-medium">{$LANG.ssoredirect}</span>
                        </div>
                    </div>
                {/if}

                <form method="post" action="{$systemurl}dologin.php" class="space-y-6">
                    <div class="space-y-2">
                        <label for="inputEmail" class="block text-sm font-medium text-gray-300">
                            <i data-lucide="mail" class="w-4 h-4 mr-2 inline text-blue-400"></i>
                            {$LANG.clientareaemail}
                        </label>
                        <input type="email"
                               name="username"
                               id="inputEmail"
                               class="w-full px-4 py-3 bg-slate-800/50 border border-slate-600/50 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 transition-all duration-300"
                               placeholder="{$LANG.enteremail}"
                               autofocus
                               required>
                    </div>

                    <div class="space-y-2">
                        <label for="inputPassword" class="block text-sm font-medium text-gray-300">
                            <i data-lucide="lock" class="w-4 h-4 mr-2 inline text-purple-400"></i>
                            {$LANG.clientareapassword}
                        </label>
                        <input type="password"
                               name="password"
                               id="inputPassword"
                               class="w-full px-4 py-3 bg-slate-800/50 border border-slate-600/50 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:border-purple-500/50 transition-all duration-300"
                               placeholder="{$LANG.clientareapassword}"
                               required>
                    </div>

                    {if $captcha}
                        <div class="space-y-2">
                            <label class="block text-sm font-medium text-gray-300">
                                <i data-lucide="shield" class="w-4 h-4 mr-2 inline text-emerald-400"></i>
                                {$LANG.captchaverify}
                            </label>
                            <div class="bg-slate-800/30 border border-slate-600/50 rounded-xl p-4">
                                {$captcha}
                            </div>
                        </div>
                    {/if}

                    <div class="flex items-center justify-between">
                        <label class="flex items-center cursor-pointer">
                            <input type="checkbox" name="rememberme" class="w-4 h-4 text-blue-500 bg-slate-800 border-slate-600 rounded focus:ring-blue-500/50 focus:ring-2">
                            <span class="ml-3 text-sm text-gray-300 hover:text-white transition-colors">{$LANG.loginrememberme}</span>
                        </label>
                        <a href="pwreset.php" class="text-sm text-blue-400 hover:text-blue-300 transition-colors duration-300 font-medium">
                            {$LANG.forgotpw}
                        </a>
                    </div>

                    <button type="submit" class="w-full bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 text-white px-8 py-4 rounded-xl font-bold text-lg shadow-2xl hover:shadow-blue-500/25 transition-all duration-300 transform hover:scale-[1.02] focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:ring-offset-2 focus:ring-offset-slate-900">
                        <i data-lucide="log-in" class="w-5 h-5 mr-2 inline"></i>
                        {$LANG.loginbutton}
                    </button>
                </form>

                <!-- Social Login Options -->
                {if $providerDisplayNames}
                    <div class="text-center my-6">
                        <div class="relative">
                            <div class="absolute inset-0 flex items-center">
                                <div class="w-full border-t border-slate-600/50"></div>
                            </div>
                            <div class="relative flex justify-center text-sm">
                                <span class="px-4 bg-slate-900/70 text-gray-400 font-medium">Or continue with</span>
                            </div>
                        </div>
                    </div>

                    <div class="space-y-3">
                        {foreach $providerDisplayNames as $provider => $displayName}
                            <a href="{$WEB_ROOT}/oauth/login.php?provider={$provider}"
                               class="w-full bg-slate-800/50 border border-slate-600/50 text-white px-6 py-3 rounded-xl font-medium hover:bg-slate-700/50 hover:border-slate-500/50 transition-all duration-300 flex items-center justify-center">
                                <i data-lucide="external-link" class="w-4 h-4 mr-2"></i>
                                {$displayName}
                            </a>
                        {/foreach}
                    </div>
                {/if}
            </div>
        </div>

        <!-- Security Info Cards -->
        <div class="mt-8 grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="bg-gradient-to-br from-emerald-500/10 to-teal-500/10 border border-emerald-500/20 rounded-2xl p-6 text-center">
                <div class="w-12 h-12 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-xl flex items-center justify-center mx-auto mb-3">
                    <i data-lucide="shield-check" class="w-6 h-6 text-white"></i>
                </div>
                <h3 class="text-lg font-bold text-white mb-2">Secure Access</h3>
                <p class="text-emerald-300 text-sm">256-bit SSL encryption protects your login credentials</p>
            </div>

            <div class="bg-gradient-to-br from-blue-500/10 to-purple-500/10 border border-blue-500/20 rounded-2xl p-6 text-center">
                <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center mx-auto mb-3">
                    <i data-lucide="zap" class="w-6 h-6 text-white"></i>
                </div>
                <h3 class="text-lg font-bold text-white mb-2">Instant Dashboard</h3>
                <p class="text-blue-300 text-sm">Access your hosting control panel in seconds</p>
            </div>
        </div>

        <!-- Footer Links -->
        <div class="mt-8 text-center">
            <p class="text-gray-400 text-sm mb-4">
                Don't have an account?
                <a href="register.php" class="text-blue-400 hover:text-blue-300 font-medium transition-colors">
                    Create one now
                </a>
            </p>
            <div class="flex items-center justify-center space-x-6 text-xs text-gray-500">
                <a href="contact.php" class="hover:text-gray-300 transition-colors">Support</a>
                <span>•</span>
                <a href="privacy.php" class="hover:text-gray-300 transition-colors">Privacy</a>
                <span>•</span>
                <a href="terms.php" class="hover:text-gray-300 transition-colors">Terms</a>
            </div>
        </div>
    </div>
          </div>
        </div>
    </main>
{include file="$template/footer.tpl"}
