/**
 * X-ZoneServers WHMCS Theme - Main Stylesheet
 * Matches the design and branding of X-ZoneServers website
 */

/* Import Inter font with fallback */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;900&display=swap');

/* Critical loading styles to prevent FOUC and infinite loading */
html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    background: #020617;
    color: #f8fafc;
    margin: 0;
    padding: 0;
    line-height: 1.6;
    min-height: 100vh;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Prevent layout shift during loading */
* {
    box-sizing: border-box;
}

/* Loading state */
.loading {
    opacity: 0.7;
    pointer-events: none;
    cursor: wait;
}

/* Basic layout to prevent layout shift */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

@media (min-width: 640px) {
    .container {
        padding: 0 1.5rem;
    }
}

@media (min-width: 1024px) {
    .container {
        padding: 0 2rem;
    }
}

/* CSS Variables for consistent theming */
:root {
    /* Brand Colors */
    --xz-primary: #0ea5e9;      /* sky-500 */
    --xz-secondary: #38bdf8;    /* sky-400 */
    --xz-accent: #a855f7;       /* purple-500 */
    --xz-accent-2: #ec4899;     /* pink-500 */
    --xz-accent-3: #10b981;     /* emerald-500 */
    
    /* Background Colors */
    --xz-bg-primary: #020617;   /* slate-950 */
    --xz-bg-secondary: #0f172a; /* slate-900 */
    --xz-bg-tertiary: #1e293b;  /* slate-800 */
    --xz-bg-card: rgba(15, 23, 42, 0.8); /* slate-900 with opacity */
    
    /* Text Colors */
    --xz-text-primary: #f8fafc;   /* slate-50 */
    --xz-text-secondary: #d1d5db; /* gray-300 */
    --xz-text-muted: #9ca3af;     /* gray-400 */
    --xz-text-disabled: #6b7280;  /* gray-500 */
    
    /* Border Colors */
    --xz-border: #334155;         /* slate-700 */
    --xz-border-light: rgba(255, 255, 255, 0.1);
    --xz-border-accent: rgba(14, 165, 233, 0.3);
    
    /* Status Colors */
    --xz-success: #10b981;        /* emerald-500 */
    --xz-warning: #f59e0b;        /* amber-500 */
    --xz-error: #ef4444;          /* red-500 */
    --xz-info: #3b82f6;           /* blue-500 */
    
    /* Shadows */
    --xz-shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
    --xz-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --xz-shadow-lg: 0 10px 25px rgba(14, 165, 233, 0.1);
    --xz-shadow-xl: 0 20px 40px rgba(0, 0, 0, 0.2);
    
    /* Gradients */
    --xz-gradient-primary: linear-gradient(135deg, var(--xz-primary), var(--xz-secondary));
    --xz-gradient-accent: linear-gradient(135deg, var(--xz-accent), var(--xz-accent-2));
    --xz-gradient-bg: linear-gradient(to bottom right, var(--xz-bg-primary), var(--xz-bg-secondary));
    --xz-gradient-hero: radial-gradient(ellipse at top, rgba(14, 165, 233, 0.15), transparent 60%);
    
    /* Typography */
    --xz-font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --xz-font-size-xs: 0.75rem;
    --xz-font-size-sm: 0.875rem;
    --xz-font-size-base: 1rem;
    --xz-font-size-lg: 1.125rem;
    --xz-font-size-xl: 1.25rem;
    --xz-font-size-2xl: 1.5rem;
    --xz-font-size-3xl: 1.875rem;
    --xz-font-size-4xl: 2.25rem;
    
    /* Spacing */
    --xz-spacing-xs: 0.25rem;
    --xz-spacing-sm: 0.5rem;
    --xz-spacing-md: 1rem;
    --xz-spacing-lg: 1.5rem;
    --xz-spacing-xl: 2rem;
    --xz-spacing-2xl: 3rem;
    
    /* Border Radius */
    --xz-radius-sm: 0.375rem;
    --xz-radius: 0.5rem;
    --xz-radius-lg: 0.75rem;
    --xz-radius-xl: 1rem;
    --xz-radius-full: 9999px;
    
    /* Transitions */
    --xz-transition: all 0.3s ease;
    --xz-transition-fast: all 0.15s ease;
    --xz-transition-slow: all 0.5s ease;
}

/* Base Styles */
* {
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: var(--xz-font-family);
    background: var(--xz-bg-primary);
    color: var(--xz-text-secondary);
    line-height: 1.6;
    margin: 0;
    padding: 0;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
}

/* Override WHMCS default body background */
body.client-area,
body.admin-area {
    background: var(--xz-bg-primary) !important;
    background-image: var(--xz-gradient-hero) !important;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    color: var(--xz-text-primary);
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: var(--xz-spacing-md);
}

h1 { font-size: var(--xz-font-size-4xl); font-weight: 900; }
h2 { font-size: var(--xz-font-size-3xl); font-weight: 700; }
h3 { font-size: var(--xz-font-size-2xl); font-weight: 600; }
h4 { font-size: var(--xz-font-size-xl); font-weight: 600; }
h5 { font-size: var(--xz-font-size-lg); font-weight: 500; }
h6 { font-size: var(--xz-font-size-base); font-weight: 500; }

p {
    margin-bottom: var(--xz-spacing-md);
    color: var(--xz-text-secondary);
}

a {
    color: var(--xz-secondary);
    text-decoration: none;
    transition: var(--xz-transition-fast);
}

a:hover {
    color: var(--xz-primary);
    text-decoration: none;
}

/* Container and Layout */
.container-fluid,
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding-left: var(--xz-spacing-md);
    padding-right: var(--xz-spacing-md);
}

@media (min-width: 640px) {
    .container-fluid,
    .container {
        padding-left: var(--xz-spacing-lg);
        padding-right: var(--xz-spacing-lg);
    }
}

@media (min-width: 1024px) {
    .container-fluid,
    .container {
        padding-left: var(--xz-spacing-xl);
        padding-right: var(--xz-spacing-xl);
    }
}

/* Cards and Panels */
.panel,
.card,
.well {
    background: var(--xz-bg-card);
    border: 1px solid var(--xz-border-light);
    border-radius: var(--xz-radius-lg);
    backdrop-filter: blur(10px);
    box-shadow: var(--xz-shadow);
    transition: var(--xz-transition);
    margin-bottom: var(--xz-spacing-lg);
}

.panel:hover,
.card:hover {
    border-color: var(--xz-border-accent);
    box-shadow: var(--xz-shadow-lg);
    transform: translateY(-2px);
}

.panel-heading,
.card-header {
    background: rgba(15, 23, 42, 0.6);
    border-bottom: 1px solid var(--xz-border-light);
    border-radius: var(--xz-radius-lg) var(--xz-radius-lg) 0 0;
    padding: var(--xz-spacing-lg);
    color: var(--xz-text-primary);
    font-weight: 600;
}

.panel-body,
.card-body {
    padding: var(--xz-spacing-lg);
}

.panel-footer,
.card-footer {
    background: rgba(15, 23, 42, 0.3);
    border-top: 1px solid var(--xz-border-light);
    border-radius: 0 0 var(--xz-radius-lg) var(--xz-radius-lg);
    padding: var(--xz-spacing-lg);
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: var(--xz-spacing-sm) var(--xz-spacing-lg);
    border-radius: var(--xz-radius);
    font-weight: 600;
    font-size: var(--xz-font-size-sm);
    text-decoration: none;
    border: none;
    cursor: pointer;
    transition: var(--xz-transition);
    position: relative;
    overflow: hidden;
}

.btn:before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s;
}

.btn:hover:before {
    left: 100%;
}

.btn-primary {
    background: var(--xz-gradient-primary);
    color: white;
    box-shadow: var(--xz-shadow);
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: var(--xz-shadow-lg);
    color: white;
}

.btn-secondary {
    background: transparent;
    color: var(--xz-secondary);
    border: 1px solid var(--xz-secondary);
}

.btn-secondary:hover {
    background: var(--xz-secondary);
    color: white;
    transform: translateY(-1px);
}

.btn-success {
    background: var(--xz-success);
    color: white;
}

.btn-warning {
    background: var(--xz-warning);
    color: white;
}

.btn-danger {
    background: var(--xz-error);
    color: white;
}

.btn-info {
    background: var(--xz-info);
    color: white;
}

.btn-lg {
    padding: var(--xz-spacing-md) var(--xz-spacing-xl);
    font-size: var(--xz-font-size-base);
}

.btn-sm {
    padding: var(--xz-spacing-xs) var(--xz-spacing-md);
    font-size: var(--xz-font-size-xs);
}

/* Forms */
.form-control,
input[type="text"],
input[type="email"],
input[type="password"],
input[type="number"],
select,
textarea {
    background: var(--xz-bg-secondary);
    border: 1px solid var(--xz-border);
    border-radius: var(--xz-radius);
    color: var(--xz-text-primary);
    padding: var(--xz-spacing-sm) var(--xz-spacing-md);
    font-size: var(--xz-font-size-sm);
    transition: var(--xz-transition);
    width: 100%;
}

.form-control:focus,
input:focus,
select:focus,
textarea:focus {
    outline: none;
    border-color: var(--xz-primary);
    box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
}

.form-control::placeholder,
input::placeholder,
textarea::placeholder {
    color: var(--xz-text-muted);
}

.form-group {
    margin-bottom: var(--xz-spacing-lg);
}

.form-label,
label {
    color: var(--xz-text-primary);
    font-weight: 500;
    margin-bottom: var(--xz-spacing-sm);
    display: block;
}

/* Tables */
.table {
    width: 100%;
    background: transparent;
    border-collapse: collapse;
    margin-bottom: var(--xz-spacing-lg);
}

.table th,
.table td {
    padding: var(--xz-spacing-md);
    border-bottom: 1px solid var(--xz-border-light);
    text-align: left;
}

.table th {
    background: var(--xz-bg-secondary);
    color: var(--xz-text-primary);
    font-weight: 600;
    border-bottom: 2px solid var(--xz-border);
}

.table tbody tr:hover {
    background: rgba(14, 165, 233, 0.05);
}

.table-striped tbody tr:nth-child(odd) {
    background: rgba(15, 23, 42, 0.3);
}

/* Alerts */
.alert {
    padding: var(--xz-spacing-md);
    border-radius: var(--xz-radius);
    margin-bottom: var(--xz-spacing-lg);
    border-left: 4px solid;
}

.alert-success {
    background: rgba(16, 185, 129, 0.1);
    border-left-color: var(--xz-success);
    color: var(--xz-success);
}

.alert-warning {
    background: rgba(245, 158, 11, 0.1);
    border-left-color: var(--xz-warning);
    color: var(--xz-warning);
}

.alert-danger {
    background: rgba(239, 68, 68, 0.1);
    border-left-color: var(--xz-error);
    color: var(--xz-error);
}

.alert-info {
    background: rgba(59, 130, 246, 0.1);
    border-left-color: var(--xz-info);
    color: var(--xz-info);
}

/* Navigation and Breadcrumbs */
.breadcrumb {
    background: transparent;
    padding: var(--xz-spacing-md) 0;
    margin-bottom: var(--xz-spacing-lg);
}

.breadcrumb-item {
    color: var(--xz-text-muted);
}

.breadcrumb-item.active {
    color: var(--xz-text-primary);
}

.breadcrumb-item + .breadcrumb-item::before {
    content: "›";
    color: var(--xz-text-muted);
    margin: 0 var(--xz-spacing-sm);
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    margin: var(--xz-spacing-xl) 0;
}

.page-link {
    background: var(--xz-bg-secondary);
    border: 1px solid var(--xz-border);
    color: var(--xz-text-secondary);
    padding: var(--xz-spacing-sm) var(--xz-spacing-md);
    margin: 0 2px;
    border-radius: var(--xz-radius);
    transition: var(--xz-transition);
}

.page-link:hover {
    background: var(--xz-primary);
    border-color: var(--xz-primary);
    color: white;
}

.page-item.active .page-link {
    background: var(--xz-primary);
    border-color: var(--xz-primary);
    color: white;
}

/* Progress Bars */
.progress {
    background: var(--xz-bg-secondary);
    border-radius: var(--xz-radius-full);
    height: 8px;
    overflow: hidden;
}

.progress-bar {
    background: var(--xz-gradient-primary);
    height: 100%;
    border-radius: var(--xz-radius-full);
    transition: width 0.6s ease;
}

/* Badges and Labels */
.badge,
.label {
    display: inline-flex;
    align-items: center;
    padding: var(--xz-spacing-xs) var(--xz-spacing-sm);
    border-radius: var(--xz-radius-full);
    font-size: var(--xz-font-size-xs);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

.badge-primary,
.label-primary {
    background: var(--xz-primary);
    color: white;
}

.badge-success,
.label-success {
    background: var(--xz-success);
    color: white;
}

.badge-warning,
.label-warning {
    background: var(--xz-warning);
    color: white;
}

.badge-danger,
.label-danger {
    background: var(--xz-error);
    color: white;
}

.badge-info,
.label-info {
    background: var(--xz-info);
    color: white;
}

/* Modals */
.modal-content {
    background: var(--xz-bg-secondary);
    border: 1px solid var(--xz-border);
    border-radius: var(--xz-radius-lg);
    box-shadow: var(--xz-shadow-xl);
}

.modal-header {
    background: var(--xz-bg-tertiary);
    border-bottom: 1px solid var(--xz-border);
    border-radius: var(--xz-radius-lg) var(--xz-radius-lg) 0 0;
    padding: var(--xz-spacing-lg);
}

.modal-title {
    color: var(--xz-text-primary);
    font-weight: 600;
}

.modal-body {
    padding: var(--xz-spacing-lg);
    color: var(--xz-text-secondary);
}

.modal-footer {
    background: var(--xz-bg-tertiary);
    border-top: 1px solid var(--xz-border);
    border-radius: 0 0 var(--xz-radius-lg) var(--xz-radius-lg);
    padding: var(--xz-spacing-lg);
}

/* Dropdowns */
.dropdown-menu {
    background: var(--xz-bg-secondary);
    border: 1px solid var(--xz-border);
    border-radius: var(--xz-radius-lg);
    box-shadow: var(--xz-shadow-lg);
    padding: var(--xz-spacing-sm);
}

.dropdown-item {
    color: var(--xz-text-secondary);
    padding: var(--xz-spacing-sm) var(--xz-spacing-md);
    border-radius: var(--xz-radius);
    transition: var(--xz-transition);
}

.dropdown-item:hover {
    background: var(--xz-primary);
    color: white;
}

/* Tabs */
.nav-tabs {
    border-bottom: 1px solid var(--xz-border);
    margin-bottom: var(--xz-spacing-lg);
}

.nav-tabs .nav-link {
    background: transparent;
    border: none;
    color: var(--xz-text-muted);
    padding: var(--xz-spacing-md) var(--xz-spacing-lg);
    border-radius: var(--xz-radius) var(--xz-radius) 0 0;
    transition: var(--xz-transition);
}

.nav-tabs .nav-link:hover {
    color: var(--xz-text-primary);
    background: rgba(14, 165, 233, 0.1);
}

.nav-tabs .nav-link.active {
    color: var(--xz-primary);
    background: var(--xz-bg-secondary);
    border-bottom: 2px solid var(--xz-primary);
}

/* Pricing Cards */
.pricing-card {
    background: var(--xz-bg-card);
    border: 1px solid var(--xz-border-light);
    border-radius: var(--xz-radius-xl);
    padding: var(--xz-spacing-xl);
    text-align: center;
    transition: var(--xz-transition);
    position: relative;
    overflow: hidden;
}

.pricing-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--xz-gradient-primary);
}

.pricing-card:hover {
    transform: translateY(-5px);
    border-color: var(--xz-border-accent);
    box-shadow: var(--xz-shadow-lg);
}

.pricing-card.featured {
    border-color: var(--xz-primary);
    box-shadow: 0 0 30px rgba(14, 165, 233, 0.2);
}

.pricing-card.featured::before {
    background: var(--xz-gradient-accent);
}

.pricing-title {
    color: var(--xz-text-primary);
    font-size: var(--xz-font-size-xl);
    font-weight: 600;
    margin-bottom: var(--xz-spacing-md);
}

.pricing-price {
    color: var(--xz-primary);
    font-size: var(--xz-font-size-4xl);
    font-weight: 900;
    margin-bottom: var(--xz-spacing-sm);
}

.pricing-period {
    color: var(--xz-text-muted);
    font-size: var(--xz-font-size-sm);
    margin-bottom: var(--xz-spacing-lg);
}

.pricing-features {
    list-style: none;
    padding: 0;
    margin: var(--xz-spacing-lg) 0;
}

.pricing-features li {
    padding: var(--xz-spacing-sm) 0;
    color: var(--xz-text-secondary);
    border-bottom: 1px solid var(--xz-border-light);
}

.pricing-features li:last-child {
    border-bottom: none;
}

.pricing-features li::before {
    content: "✓";
    color: var(--xz-success);
    font-weight: bold;
    margin-right: var(--xz-spacing-sm);
}

/* Pricing Grid */
.pricing-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--xz-spacing-xl);
    margin: var(--xz-spacing-2xl) 0;
}

/* Feature Cards */
.feature-card {
    background: var(--xz-bg-card);
    border: 1px solid var(--xz-border-light);
    border-radius: var(--xz-radius-lg);
    padding: var(--xz-spacing-xl);
    transition: var(--xz-transition);
    backdrop-filter: blur(10px);
}

.feature-card:hover {
    transform: translateY(-2px);
    border-color: var(--xz-border-accent);
    box-shadow: var(--xz-shadow-lg);
}

/* Skip Link for Accessibility */
.skip-link {
    position: absolute;
    left: -10000px;
    top: auto;
    width: 1px;
    height: 1px;
    overflow: hidden;
    background: var(--xz-primary);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: var(--xz-radius);
    text-decoration: none;
    font-weight: 600;
    z-index: 1000;
    transition: var(--xz-transition-fast);
}

.skip-link:focus {
    position: fixed;
    left: 1rem;
    top: 1rem;
    width: auto;
    height: auto;
    clip: auto;
    overflow: visible;
    box-shadow: var(--xz-shadow-lg);
}

/* Login page specific enhancements */
.login-page-section {
    background: linear-gradient(135deg, #020617 0%, #0f172a 50%, #020617 100%);
    min-height: 100vh;
}

.login-hero-gradient {
    background: radial-gradient(ellipse at top, rgba(14, 165, 233, 0.15), transparent 60%);
}

.login-form-enhanced input[type="email"],
.login-form-enhanced input[type="password"] {
    background: rgba(30, 41, 59, 0.5);
    border: 1px solid rgba(51, 65, 85, 0.5);
    color: #f8fafc;
    transition: all 0.3s ease;
}

.login-form-enhanced input[type="email"]:focus,
.login-form-enhanced input[type="password"]:focus {
    border-color: rgba(59, 130, 246, 0.5);
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
    background: rgba(30, 41, 59, 0.7);
}

.login-btn-gradient {
    background: linear-gradient(to right, #3b82f6, #8b5cf6, #ec4899);
    transition: all 0.3s ease;
    box-shadow: 0 10px 25px rgba(59, 130, 246, 0.2);
}

.login-btn-gradient:hover {
    transform: scale(1.02);
    box-shadow: 0 20px 40px rgba(59, 130, 246, 0.3);
}
