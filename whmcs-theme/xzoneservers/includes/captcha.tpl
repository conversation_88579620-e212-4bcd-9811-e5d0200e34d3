{if $captcha->isEnabled()}
    <div class="captcha-container">
        {if $captcha->recaptcha->isEnabled()}
            {$captcha->recaptcha->getHtml()}
        {else}
            <p>{$LANG.captchaverify}</p>
            <div class="row">
                <div class="col-md-8">
                    <img id="inputCaptchaImage" src="{$captcha->getImgUrl()}" alt="{$LANG.captchaverify}" class="captcha-image" />
                </div>
                <div class="col-md-4">
                    <a href="#" onclick="refreshCaptchaImage(); return false;" class="btn btn-default btn-sm">
                        <i class="fas fa-sync-alt"></i> {$LANG.refresh}
                    </a>
                </div>
            </div>
            <div class="form-group">
                <input type="text" name="code" id="inputCaptcha" class="form-control" placeholder="{$LANG.captchaverify}" autocomplete="off" />
            </div>
        {/if}
    </div>
{/if}

<script type="text/javascript">
function refreshCaptchaImage() {
    var captchaImage = document.getElementById('inputCaptchaImage');
    if (captchaImage) {
        captchaImage.src = captchaImage.src.split('?')[0] + '?refresh=' + Math.random();
    }
}
</script>
